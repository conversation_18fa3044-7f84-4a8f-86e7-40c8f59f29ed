package routes

import (
	"net/http"
	"whatsapp/pkg/domains/purchase"
	"whatsapp/pkg/dtos"
	middlewares "whatsapp/pkg/infrastructure/middleware"
	"whatsapp/pkg/state"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

func PurchaseRoutes(r *gin.RouterGroup, s purchase.Service) {
	purchase := r.Group("/purchases")
	purchase.POST("", middlewares.Authorized(), createPurchase(s))
	purchase.GET("", middlewares.Authorized(), getUserPurchases(s))
	purchase.GET("/intro-eligibility", middlewares.Authorized(), checkIntroPackageEligibility(s))
	purchase.PUT("/:id/complete", middlewares.Authorized(), completePurchase(s))
}

// createPurchase handles the creation of a new purchase.
// @Summary Create purchase
// @Description This endpoint creates a new purchase.
// @Tags Purchase
// @Accept  json
// @Produce  json
// @Param body body dtos.CreatePurchaseReq true "Purchase Request"
// @Success 200 {object} map[string]string "success message"
// @Failure 400 {object} map[string]string "error message"
// @Router /purchases [post]
func createPurchase(s purchase.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.CreatePurchaseReq
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		userId := state.GetCurrentUserID(c)
		req.UserId = userId

		err := s.CreatePurchase(c, req)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Purchase created successfully"})
	}
}

// getUserPurchases handles the retrieval of user purchases.
// @Summary Get user purchases
// @Description This endpoint retrieves all purchases for the current user.
// @Tags Purchase
// @Accept  json
// @Produce  json
// @Success 200 {array} entities.Purchase "user purchases"
// @Failure 500 {object} map[string]string "error message"
// @Router /purchases [get]
func getUserPurchases(s purchase.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		userId := state.GetCurrentUserID(c)

		purchases, err := s.GetUserPurchases(c, userId)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, purchases)
	}
}

// checkIntroPackageEligibility handles checking if user is eligible for intro packages.
// @Summary Check intro package eligibility
// @Description This endpoint checks if the user is eligible for intro packages.
// @Tags Purchase
// @Accept  json
// @Produce  json
// @Success 200 {object} dtos.IntroPackageEligibilityRes "eligibility status"
// @Failure 500 {object} map[string]string "error message"
// @Router /purchases/intro-eligibility [get]
func checkIntroPackageEligibility(s purchase.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		userId := state.GetCurrentUserID(c)

		eligibility, err := s.CheckIntroPackageEligibility(c, userId)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, eligibility)
	}
}

// completePurchase handles completing a purchase.
// @Summary Complete purchase
// @Description This endpoint marks a purchase as completed.
// @Tags Purchase
// @Accept  json
// @Produce  json
// @Param id path string true "Purchase ID"
// @Success 200 {object} map[string]string "success message"
// @Failure 400 {object} map[string]string "error message"
// @Router /purchases/{id}/complete [put]
func completePurchase(s purchase.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		purchaseIdStr := c.Param("id")
		purchaseId, err := uuid.Parse(purchaseIdStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid purchase ID"})
			return
		}

		err = s.CompletePurchase(c, purchaseId)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Purchase completed successfully"})
	}
}
