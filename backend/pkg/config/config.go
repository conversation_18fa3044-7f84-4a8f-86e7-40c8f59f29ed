package config

import (
	"os"

	"gopkg.in/yaml.v3"
)

type Config struct {
	AppName      string   `yaml:"app_name"`
	Host         string   `yaml:"host"`
	Port         string   `yaml:"port"`
	GrpcPort     string   `yaml:"grpc_port"`
	AllowMethods []string `yaml:"allow_methods"`
	AllowHeaders []string `yaml:"allow_headers"`
	AllowOrigins []string `yaml:"allow_origins"`
	WpApiUrl     string   `yaml:"wp_api_url"`
	ApiKey       string   `yaml:"api_key"`
	JwtIssuer    string   `yaml:"jwt_issuer"`
	JwtExpire    int      `yaml:"jwt_expire"`
	JwtSecret    string   `yaml:"jwt_secret"`
	Database     Database `yaml:"database"`
	Redis        Redis    `yaml:"redis"`
	CancelLink   string   `yaml:"cancel_link"`
}

type Database struct {
	Host     string `yaml:"host"`
	Port     string `yaml:"port"`
	User     string `yaml:"user"`
	Password string `yaml:"password"`
	Name     string `yaml:"name"`
	SslMode  string `yaml:"sslmode"`
	Migrate  bool   `yanl:"migrate"`
}

type Redis struct {
	Host     string `yaml:"host"`
	Port     string `yaml:"port"`
	Password string `yaml:"password"`
	DB       int    `yaml:"db"`
}

func ReadValue() *Config {
	var configs Config
	fileName := "/config.yaml" // Artık sabit
	yamlFile, err := os.ReadFile(fileName)
	if err != nil {
		panic("config.yaml okunamadı: " + err.Error())
	}
	yaml.Unmarshal(yamlFile, &configs)
	return &configs
}
