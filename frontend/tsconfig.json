{"compilerOptions": {"target": "es2015", "lib": ["dom", "dom.iterable", "es6", "es2015"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "downlevelIteration": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@pages/*": ["src/pages/*"], "@components/*": ["src/components/*"], "@layouts/*": ["src/layouts/*"]}}, "include": ["src"]}