{"name": "frontend", "private": true, "version": "0.0.0", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint ."}, "dependencies": {"@types/qrcode": "^1.5.5", "ajv": "^8.17.1", "axios": "^1.10.0", "clsx": "^2.1.1", "i18next": "^25.2.1", "lucide-react": "^0.523.0", "qrcode": "^1.5.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.5.3", "react-icons": "^5.5.0", "react-is": "^18.3.1", "react-router-dom": "^6.28.0", "recharts": "^3.0.2", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/js": "^8.57.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22.16.5", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "autoprefixer": "^10.4.21", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.5.6", "react-scripts": "^5.0.1", "tailwindcss": "^3.4.17", "typescript": "^5.0.0"}, "proxy": "http://localhost:3010", "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}