{"name": "frontend-web", "version": "0.1.0", "private": true, "dependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.8", "@tailwindcss/typography": "^0.5.16", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@types/react-router-dom": "^5.3.3", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "lucide-react": "^0.511.0", "postcss": "^8.5.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.5.2", "react-router-dom": "^7.6.1", "react-scripts": "5.0.1", "tailwindcss": "^3.4.17", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"typescript": "5.1.6"}, "useEffect": {"description": "React hook to perform side effects in function components.", "syntax": "useEffect(() => { /* side effect code */ }, [/* dependencies */]);", "example": "useEffect(() => { document.title = t('dashboard.title'); }, [t]);"}}