import { api } from '../lib/api';

export interface IntroPackageEligibility {
  is_eligible: boolean;
  message: string;
}

export interface CreatePurchaseRequest {
  package_type: string;
  package_id: string;
  amount: number;
  price: number;
  currency: string;
}

export interface Purchase {
  id: string;
  user_id: string;
  package_type: string;
  package_id: string;
  amount: number;
  price: number;
  currency: string;
  status: string;
  created_at: string;
}

export const purchaseService = {
  async checkIntroPackageEligibility(): Promise<IntroPackageEligibility> {
    const response = await api.get('/purchases/intro-eligibility');
    return response.data;
  },

  async createPurchase(request: CreatePurchaseRequest): Promise<void> {
    await api.post('/purchases', request);
  },

  async getUserPurchases(): Promise<Purchase[]> {
    const response = await api.get('/purchases');
    return response.data;
  },

  async completePurchase(purchaseId: string): Promise<void> {
    await api.put(`/purchases/${purchaseId}/complete`);
  }
};
