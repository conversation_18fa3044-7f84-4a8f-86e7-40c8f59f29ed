@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles */
* {
  border-color: rgb(229 231 235);
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  overflow-x: hidden; /* Prevent horizontal scroll */
}

body {
  background-color: rgb(17 24 39);
  color: rgb(17 24 39);
  font-family: system-ui, -apple-system, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  height: 100%;
  width: 100%;
  min-height: 100vh;
}

/* Component styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  padding: 0.5rem 1rem;
  cursor: pointer;
  border: none;
}

.btn:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px rgb(59 130 246);
}

.btn:disabled {
  opacity: 0.5;
  pointer-events: none;
}

.btn-primary {
  background-color: rgb(37 99 235);
  color: white;
}

.btn-primary:hover {
  background-color: rgb(29 78 216);
}

.btn-secondary {
  background-color: rgb(229 231 235);
  color: rgb(17 24 39);
}

.btn-secondary:hover {
  background-color: rgb(209 213 219);
}

.btn-success {
  background-color: rgb(34 197 94);
  color: white;
}

.btn-success:hover {
  background-color: rgb(22 163 74);
}

.btn-danger {
  background-color: rgb(239 68 68);
  color: white;
}

.btn-danger:hover {
  background-color: rgb(220 38 38);
}

.card {
  border-radius: 0.5rem;
  border: 1px solid rgb(229 231 235);
  background-color: white;
  padding: 1.5rem;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

.input {
  display: flex;
  height: 2.5rem;
  width: 100%;
  border-radius: 0.375rem;
  border: 1px solid rgb(209 213 219);
  background-color: white;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.input::placeholder {
  color: rgb(107 114 128);
}

.input:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px rgb(59 130 246);
}

.input:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* Responsive utilities */
.container-responsive {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container-responsive {
    max-width: 640px;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 768px) {
  .container-responsive {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container-responsive {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container-responsive {
    max-width: 1280px;
  }
}

/* Mobile-first responsive text */
.text-responsive-sm {
  font-size: 0.875rem;
}

@media (min-width: 768px) {
  .text-responsive-sm {
    font-size: 1rem;
  }
}

.text-responsive-base {
  font-size: 1rem;
}

@media (min-width: 768px) {
  .text-responsive-base {
    font-size: 1.125rem;
  }
}

.text-responsive-lg {
  font-size: 1.125rem;
}

@media (min-width: 768px) {
  .text-responsive-lg {
    font-size: 1.25rem;
  }
}

.text-responsive-xl {
  font-size: 1.25rem;
}

@media (min-width: 768px) {
  .text-responsive-xl {
    font-size: 1.5rem;
  }
}

.text-responsive-2xl {
  font-size: 1.5rem;
}

@media (min-width: 768px) {
  .text-responsive-2xl {
    font-size: 1.875rem;
  }
}

.text-responsive-3xl {
  font-size: 1.875rem;
}

@media (min-width: 768px) {
  .text-responsive-3xl {
    font-size: 2.25rem;
  }
}

/* Mobile spacing utilities */
.space-responsive-sm > * + * {
  margin-top: 0.5rem;
}

@media (min-width: 768px) {
  .space-responsive-sm > * + * {
    margin-top: 1rem;
  }
}

.space-responsive-base > * + * {
  margin-top: 1rem;
}

@media (min-width: 768px) {
  .space-responsive-base > * + * {
    margin-top: 1.5rem;
  }
}

.space-responsive-lg > * + * {
  margin-top: 1.5rem;
}

@media (min-width: 768px) {
  .space-responsive-lg > * + * {
    margin-top: 2rem;
  }
}

/* Touch-friendly button sizing */
.btn-touch {
  min-height: 44px;
  min-width: 44px;
  padding: 0.75rem 1rem;
}

@media (min-width: 768px) {
  .btn-touch {
    min-height: auto;
    min-width: auto;
    padding: 0.5rem 1rem;
  }
}

/* Prevent text selection on mobile for UI elements */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Safe area padding for mobile devices */
.safe-area-padding {
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
}

/* Smooth scrolling */
.smooth-scroll {
  scroll-behavior: smooth;
}

/* Hide scrollbar but keep functionality */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
