import { useEffect, useState } from 'react';
import { X, CheckCircle, AlertCircle } from 'lucide-react';
import { Notification as NotificationType } from '../contexts/NotificationContext';

interface NotificationProps {
  notification: NotificationType;
  onRemove: (id: string) => void;
}

export function Notification({ notification, onRemove }: NotificationProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isLeaving, setIsLeaving] = useState(false);

  useEffect(() => {
    // Trigger entrance animation
    const timer = setTimeout(() => setIsVisible(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const handleRemove = () => {
    setIsLeaving(true);
    setTimeout(() => {
      onRemove(notification.id);
    }, 300); // Match the animation duration
  };

  const getIcon = () => {
    if (notification.type === 'success') {
      return <CheckCircle className="w-5 h-5 text-green-400" />;
    }
    return <AlertCircle className="w-5 h-5 text-red-400" />;
  };

  const getBackgroundColor = () => {
    if (notification.type === 'success') {
      return 'bg-green-900/90 border-green-700';
    }
    return 'bg-red-900/90 border-red-700';
  };

  const getTextColor = () => {
    if (notification.type === 'success') {
      return 'text-green-100';
    }
    return 'text-red-100';
  };

  return (
    <div
      className={`
        flex items-center gap-3 p-4 rounded-lg border backdrop-blur-sm
        transition-all duration-300 ease-in-out transform
        ${getBackgroundColor()}
        ${getTextColor()}
        ${isVisible && !isLeaving 
          ? 'translate-x-0 opacity-100' 
          : 'translate-x-full opacity-0'
        }
        min-w-[300px] max-w-[400px]
        shadow-lg
      `}
    >
      {/* Icon */}
      <div className="flex-shrink-0">
        {getIcon()}
      </div>

      {/* Message */}
      <div className="flex-1 text-sm font-medium">
        {notification.message}
      </div>

      {/* Close Button */}
      <button
        onClick={handleRemove}
        className={`
          flex-shrink-0 p-1 rounded-full transition-colors duration-200
          ${notification.type === 'success' 
            ? 'hover:bg-green-800/50 text-green-300 hover:text-green-200' 
            : 'hover:bg-red-800/50 text-red-300 hover:text-red-200'
          }
        `}
      >
        <X className="w-4 h-4" />
      </button>
    </div>
  );
}
