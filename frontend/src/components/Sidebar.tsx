import React from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import {
  BarChart3,
  Send,
  UserX,
  Clock,
  Calendar,
  CheckCircle,
  ChevronLeft,
  ChevronRight,
  LogOut,
  Settings,
  Smartphone,
  Users,
  Activity,
  Zap,
  ShoppingCart
} from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'

const cn = (...classes: string[]) => classes.filter(Boolean).join(' ')

interface SidebarItem {
  name: string
  href: string
  icon: React.ComponentType<any>
  count?: number
  badge?: string
  color?: string
}



interface SidebarProps {
  isCollapsed: boolean
  setIsCollapsed: (collapsed: boolean) => void
  isMobile?: boolean
  onMobileClose?: () => void
}

export default function Sidebar({
  isCollapsed,
  setIsCollapsed,
  isMobile = false,
  onMobileClose
}: SidebarProps) {
  const navigate = useNavigate()
  const location = useLocation()
  const { user, logout } = useAuth()
  const { t } = useTranslation()

  const activeItem = location.pathname

  const handleNavigation = (href: string) => {
    navigate(href)
    if (isMobile && onMobileClose) {
      onMobileClose()
    }
  }

  const sidebarItems: SidebarItem[] = [
    { name: t('navigation.dashboard'), href: '/dashboard', icon: BarChart3, color: 'text-blue-400' },
    { name: t('navigation.sendMessage'), href: '/send-message', icon: Send, color: 'text-emerald-400' },
    { name: t('navigation.blacklist'), href: '/blacklist', icon: UserX, count: 12, color: 'text-red-400' },
    { name: t('navigation.waitingMessages'), href: '/waiting-messages', icon: Clock, count: 5, color: 'text-yellow-400' },
    { name: t('navigation.futureMessages'), href: '/future-messages', icon: Calendar, count: 8, color: 'text-purple-400' },
    { name: t('navigation.sentMessages'), href: '/sent-messages', icon: CheckCircle, color: 'text-green-400' },
    { name: t('navigation.devices'), href: '/devices', icon: Smartphone, badge: 'Pro', color: 'text-cyan-400' },
    { name: t('navigation.groups'), href: '/groups', icon: Users, color: 'text-orange-400' },
    { name: t('navigation.packages'), href: '/packages', icon: ShoppingCart, color: 'text-purple-400' },
  ]

  const handleLogout = () => {
    logout()
    window.location.href = '/login'
  }

  return (
    <div className={cn(
      "bg-gray-900/95 backdrop-blur-xl text-white transition-all duration-300 ease-in-out flex flex-col relative h-full",
      isMobile
        ? "w-72" // Fixed width on mobile
        : isCollapsed
          ? "w-20"
          : "w-72"
    )}>
      {/* Background Overlay */}
      <div className="absolute inset-0 bg-gray-900 opacity-90 pointer-events-none"></div>
      
      <div className="relative z-10 flex flex-col h-full">
        {/* Header */}
        <div className="p-4 md:p-6">
          <div className="flex items-center justify-between">
            {(!isCollapsed || isMobile) && (
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-emerald-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Zap className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-lg md:text-xl font-bold text-emerald-400">
                    {t('sidebar.appName')}
                  </h1>
                  <p className="text-xs text-gray-400">{t('sidebar.appSubtitle')}</p>
                </div>
              </div>
            )}
            {!isMobile && (
              <button
                onClick={() => setIsCollapsed(!isCollapsed)}
                className="p-2.5 rounded-xl hover:bg-gray-800/50 transition-all duration-200 group"
              >
                {isCollapsed ? (
                  <ChevronRight className="h-5 w-5 text-gray-400 group-hover:text-white transition-colors" />
                ) : (
                  <ChevronLeft className="h-5 w-5 text-gray-400 group-hover:text-white transition-colors" />
                )}
              </button>
            )}
            {isMobile && (
              <button
                onClick={onMobileClose}
                className="p-2.5 rounded-xl hover:bg-gray-800/50 transition-all duration-200 group"
              >
                <ChevronLeft className="h-5 w-5 text-gray-400 group-hover:text-white transition-colors" />
              </button>
            )}
          </div>
        </div>

        {/* User Profile */}
        {user && (
          <div className={cn(
            "p-4 md:p-6 transition-all duration-300",
            isCollapsed && !isMobile ? "px-4" : ""
          )}>
            {isCollapsed && !isMobile ? (
              // Collapsed view - only avatar with dropdown-style buttons
              <div className="flex flex-col items-center space-y-3">
                <div className="relative">
                  {user.avatar ? (
                    <img
                      src={user.avatar}
                      alt="Avatar"
                      className="w-8 h-8 rounded-xl object-cover shadow-lg"
                    />
                  ) : (
                    <div className="w-8 h-8 bg-emerald-600 rounded-xl flex items-center justify-center shadow-lg">
                      <span className="text-lg font-bold text-white">
                        {user.first_name.charAt(0)}{user.last_name.charAt(0)}
                      </span>
                    </div>
                  )}
                </div>

                {/* Collapsed Action Buttons */}
                <div className="flex flex-col space-y-1">
                  <button
                    onClick={() => navigate('/settings')}
                    className="p-2 rounded-lg text-gray-400 hover:bg-gray-700/50 hover:text-white transition-all duration-200"
                    title={t('sidebar.settings')}
                  >
                    <Settings className="h-4 w-4" />
                  </button>
                  <button
                    onClick={handleLogout}
                    className="p-2 rounded-lg text-gray-400 hover:bg-red-500/20 hover:text-red-400 transition-all duration-200"
                    title={t('sidebar.logout')}
                  >
                    <LogOut className="h-4 w-4" />
                  </button>
                </div>
              </div>
            ) : (
              // Expanded view - full user info with side buttons
              <div className="flex items-center space-x-4">
                <div className="relative">
                  {user.avatar ? (
                    <img
                      src={user.avatar}
                      alt="Avatar"
                      className="w-9 h-9 rounded-xl object-cover shadow-lg"
                    />
                  ) : (
                    <div className="w-9 h-9 bg-emerald-600 rounded-xl flex items-center justify-center shadow-lg">
                      <span className="text-lg font-bold text-white">
                        {user.first_name.charAt(0)}{user.last_name.charAt(0)}
                      </span>
                    </div>
                  )}
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <p className="text-sm font-semibold text-white truncate">
                          {user ? `${user.first_name} ${user.last_name}` : t('auth.defaultUser')}
                        </p>
                      </div>
                      <div className="flex items-center space-x-1 mt-1">
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex items-center space-x-1 ml-2">
                      <button
                        onClick={() => navigate('/settings')}
                        className="p-2 rounded-lg text-gray-400 hover:bg-gray-700/50 hover:text-white transition-all duration-200"
                        title={t('navigation.settings')}
                      >
                        <Settings className="h-4 w-4" />
                      </button>
                      <button
                        onClick={handleLogout}
                        className="p-2 rounded-lg text-gray-400 hover:bg-red-500/20 hover:text-red-400 transition-all duration-200"
                        title={t('auth.logout')}
                      >
                        <LogOut className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Navigation */}
        <nav className="flex-1 p-3 md:p-4 space-y-2 overflow-y-auto">
          {sidebarItems.map((item) => {
            const isActive = activeItem === item.href
            const Icon = item.icon

            return (
              <button
                key={item.name}
                onClick={() => handleNavigation(item.href)}
                className={cn(
                  "group flex items-center w-full px-3 md:px-4 py-3 rounded-xl transition-all duration-200 relative overflow-hidden",
                  isActive
                    ? "bg-emerald-500/20 text-white border border-emerald-500/30 shadow-lg shadow-emerald-500/10"
                    : "text-gray-400 hover:bg-gray-800/50 hover:text-white",
                  isCollapsed && !isMobile ? "justify-center px-3" : ""
                )}
                title={isCollapsed && !isMobile ? item.name : undefined}
              >
                {/* Active indicator */}
                {isActive && (
                  <div className="absolute left-0 top-0 bottom-0 w-1 bg-emerald-500 rounded-r-full"></div>
                )}
                
                <div className={cn(
                  "flex items-center transition-all duration-200",
                  isCollapsed && !isMobile ? "justify-center" : "space-x-3 w-full"
                )}>
                  <div className={cn(
                    "p-2 rounded-lg transition-all duration-200",
                    isActive ? "bg-white/10" : "group-hover:bg-gray-700/50"
                  )}>
                    <Icon className={cn(
                      "h-5 w-5 transition-colors duration-200",
                      isActive ? "text-white" : item.color || "text-gray-400"
                    )} />
                  </div>

                  {(!isCollapsed || isMobile) && (
                    <>
                      <span className="flex-1 text-left font-medium text-sm md:text-base">{item.name}</span>
                    </>
                  )}
                </div>
              </button>
            )
          })}
        </nav>





        {/* Status Bar */}
        {(!isCollapsed || isMobile) && (
          <div className="p-3 md:p-4 bg-gray-800/30">
            <div className="flex items-center justify-between text-xs text-gray-400">
              <div className="flex items-center space-x-2">
                <Activity className="h-4 w-4 text-emerald-400" />
                <span className="hidden sm:inline">{t('sidebar.systemActive')}</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                <span className="hidden sm:inline">{t('common.online')}</span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}